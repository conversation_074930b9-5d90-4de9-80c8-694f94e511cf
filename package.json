{"name": "campus-connect-user", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@yeger/vue-masonry-wall": "^5.0.17", "axios": "^1.9.0", "element-plus": "^2.4.3", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-masonry-css": "^1.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass-embedded": "^1.89.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}